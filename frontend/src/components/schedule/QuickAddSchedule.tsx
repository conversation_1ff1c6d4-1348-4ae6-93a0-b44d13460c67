'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Plus,
  X,
  Save,
  Calendar,
  Clock,
  MapPin,
  User,
  BookOpen
} from 'lucide-react'

interface QuickAddProps {
  selectedDate?: Date
  onClose: () => void
  onSave: (schedule: any) => void
}

export default function QuickAddSchedule({ selectedDate, onClose, onSave }: QuickAddProps) {
  const [formData, setFormData] = useState({
    subject: '',
    teacher: '',
    room: '',
    class: '',
    startTime: '',
    endTime: '',
    type: 'theory'
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      // Validate form
      if (!formData.subject || !formData.teacher || !formData.room || !formData.class) {
        setError('Vui lòng điền đầy đủ thông tin')
        return
      }

      const scheduleData = {
        ...formData,
        date: selectedDate?.toISOString().split('T')[0] || new Date().toISOString().split('T')[0],
        id: Date.now().toString() // Mock ID
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      onSave(scheduleData)
      onClose()
    } catch (err) {
      setError('Có lỗi xảy ra khi tạo lịch giảng')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Plus className="h-5 w-5 mr-2" />
                Thêm lịch giảng nhanh
              </CardTitle>
              <CardDescription>
                {selectedDate 
                  ? `Ngày: ${selectedDate.toLocaleDateString('vi-VN')}`
                  : 'Tạo lịch giảng mới'
                }
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Subject and Class */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="subject" className="flex items-center">
                  <BookOpen className="h-4 w-4 mr-2" />
                  Môn học
                </Label>
                <Input
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  placeholder="Nhập tên môn học"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="class">Lớp</Label>
                <Input
                  id="class"
                  name="class"
                  value={formData.class}
                  onChange={handleInputChange}
                  placeholder="Nhập tên lớp"
                  required
                />
              </div>
            </div>

            {/* Teacher and Room */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="teacher" className="flex items-center">
                  <User className="h-4 w-4 mr-2" />
                  Giảng viên
                </Label>
                <Input
                  id="teacher"
                  name="teacher"
                  value={formData.teacher}
                  onChange={handleInputChange}
                  placeholder="Nhập tên giảng viên"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="room" className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2" />
                  Phòng học
                </Label>
                <Input
                  id="room"
                  name="room"
                  value={formData.room}
                  onChange={handleInputChange}
                  placeholder="Nhập phòng học"
                  required
                />
              </div>
            </div>

            {/* Time and Type */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startTime" className="flex items-center">
                  <Clock className="h-4 w-4 mr-2" />
                  Giờ bắt đầu
                </Label>
                <Input
                  id="startTime"
                  name="startTime"
                  type="time"
                  value={formData.startTime}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="endTime">Giờ kết thúc</Label>
                <Input
                  id="endTime"
                  name="endTime"
                  type="time"
                  value={formData.endTime}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Hình thức</Label>
                <select
                  id="type"
                  name="type"
                  value={formData.type}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="theory">Lý thuyết</option>
                  <option value="practice">Thực hành</option>
                  <option value="discussion">Thảo luận</option>
                </select>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Hủy
              </Button>
              <Button type="submit" disabled={loading}>
                <Save className="h-4 w-4 mr-2" />
                {loading ? 'Đang lưu...' : 'Lưu lịch giảng'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
