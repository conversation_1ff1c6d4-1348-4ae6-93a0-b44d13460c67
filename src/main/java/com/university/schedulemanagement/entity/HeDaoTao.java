package com.university.schedulemanagement.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;

/**
 * HeDaoTao Entity - Hệ đào tạo
 */
@Entity
@Table(name = "HE_DAO_TAO")
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class HeDaoTao {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_HE_DAO_TAO")
    private Long idHeDaoTao;

    @Column(name = "TEN_HE_DAO_TAO", nullable = false, length = 100)
    private String tenHeDaoTao;

    @Column(name = "MO_TA", length = 250)
    private String moTa;

    @OneToMany(mappedBy = "heDaoTao", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<MonHoc> monHocList;

}
