package com.university.schedulemanagement.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * SubjectResponse - DTO response môn học
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubjectResponse {

    private Long id;

    private String maMonHoc;

    private String tenMonHoc;

    private String tenLoaiMonHoc;

    private String tenKhoa;

    private String tenHeDaoTao;

    private String tenNhomLk;

    private Integer soTietLt;

    private Integer soTietTh;

    private Integer soTietTu;

    private Boolean monDieuKien;

    private Boolean monTn;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}
