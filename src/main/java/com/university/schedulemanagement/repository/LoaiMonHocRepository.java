package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * LoaiMonHocRepository - Repository cho Loại môn học
 */
@Repository
public interface LoaiMonHocRepository extends JpaRepository<LoaiMonHoc, Long> {

    Optional<LoaiMonHoc> findByTenLoai(String tenLoai);

    @Query("SELECT lmh FROM LoaiMonHoc lmh WHERE " +
            "LOWER(lmh.tenLoai) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    List<LoaiMonHoc> findByKeyword(@Param("keyword") String keyword);
}
