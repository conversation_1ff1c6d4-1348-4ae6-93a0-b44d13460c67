package com.university.schedulemanagement.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * LoginResponse - DTO response đăng nhập
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoginResponse {

    private String token;

    private String tokenType = "Bearer";

    private Long expiresIn;

    private TeacherInfo userInfo;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TeacherInfo {
        private Long id;
        private String maCanBo;
        private String ten;
        private String email;
        private String sdt;
        private String tenVaiTro;
        private String tenKhoa;
        private Boolean nu;
    }
}
