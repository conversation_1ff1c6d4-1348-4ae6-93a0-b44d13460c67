# 🎓 Hệ Thống Quản Lý Lịch Giảng

## 📋 Tổng Quan

Hệ thống quản lý lịch giảng và tổng hợp giờ giảng cho các trường đạ<PERSON> họ<PERSON>, đ<PERSON><PERSON><PERSON> xây dựng bằng Spring Boot với các tính năng:

- ✅ Quản lý lịch giảng theo quy trình 7 bước
- ✅ Tự động tính toán và tổng hợp giờ giảng
- ✅ Phân quyền theo vai trò (Admin, Trưởng khoa, Giảng viên)
- ✅ Xuất báo cáo Excel
- ✅ RESTful API với JWT Authentication
- ✅ Swagger UI documentation

## 🚀 Công Nghệ Sử Dụng

- **Backend**: Spring Boot 2.7.18
- **Database**: MySQL 8.0
- **Authentication**: JWT (JSON Web Token)
- **Documentation**: Swagger/OpenAPI 3
- **Build Tool**: Maven
- **Java Version**: 8+

## 📦 Cài Đặt và Chạy

### 1. <PERSON><PERSON><PERSON>
- Java 8 hoặc cao hơn
- Maven 3.6+
- MySQL 8.0+

### 2. Cấu Hình Database
```sql
CREATE DATABASE lich_giang_db;
CREATE USER 'root'@'localhost' IDENTIFIED BY 'root';
GRANT ALL PRIVILEGES ON lich_giang_db.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

### 3. Chạy Ứng Dụng
```bash
mvn spring-boot:run
```

### 4. Truy Cập
- **API Base URL**: http://localhost:8080/api
- **Swagger UI**: http://localhost:8080/api/swagger-ui.html
- **Health Check**: http://localhost:8080/api/health

## 👥 Tài Khoản Mặc Định

| Vai Trò | Username | Password | Quyền |
|---------|----------|----------|-------|
| Admin | `admin` | `123456` | Toàn quyền |
| Trưởng Khoa | `truongkhoa` | `123456` | Quản lý khoa |
| Giảng Viên | `giangvien` | `123456` | Xem cá nhân |

## 📚 Tài Liệu API

### 📖 Hướng Dẫn Chi Tiết
- [API Usage Guide](API_USAGE_GUIDE.md) - Hướng dẫn sử dụng API
- [API Examples](API_EXAMPLES.md) - Ví dụ và use cases

### 🔗 Quick Links
- **Swagger UI**: http://localhost:8080/api/swagger-ui.html
- **API Docs**: http://localhost:8080/api/api-docs

## 🔐 Authentication

### Đăng Nhập
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"maCanBo":"admin","matKhau":"123456"}'
```

### Sử Dụng Token
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8080/api/auth/me
```

## 📅 Chức Năng Chính

### 1. Quản Lý Lịch Giảng
- Tạo, sửa, xóa lịch giảng
- Kiểm tra xung đột thời gian
- Quản lý phòng học khả dụng
- Phân công giảng viên

### 2. Tổng Hợp Giờ Giảng
- Tự động tính toán giờ LT/TH
- Áp dụng hệ số quy đổi
- Báo cáo theo giảng viên/khoa
- Thống kê theo kỳ học

### 3. Xuất Báo Cáo
- Lịch giảng cá nhân (Excel)
- Báo cáo giờ giảng (Excel)
- Lịch giảng theo lớp
- Tổng hợp toàn trường

## 🔒 Phân Quyền

### ADMIN
- ✅ Quản lý toàn bộ hệ thống
- ✅ Tạo/sửa/xóa lịch giảng
- ✅ Xem báo cáo tất cả khoa

### TRUONG_KHOA
- ✅ Quản lý lịch giảng khoa
- ✅ Tính toán giờ giảng khoa
- ✅ Xem báo cáo khoa

### GIANG_VIEN
- ✅ Xem lịch giảng cá nhân
- ✅ Xem giờ giảng cá nhân
- ✅ Xuất báo cáo cá nhân

## 🧪 Testing

### Health Check
```bash
curl http://localhost:8080/api/health
```

### API Testing với Postman
Import collection từ các file hướng dẫn API

## 🐛 Troubleshooting

### Lỗi Thường Gặp

1. **Database Connection Error**
   - Kiểm tra MySQL service
   - Xác nhận username/password

2. **JWT Token Error**
   - Token hết hạn (24h)
   - Sai format Bearer token

3. **Permission Denied**
   - Kiểm tra role của user
   - Xác nhận endpoint permissions

## 📞 Liên Hệ

- **Email**: <EMAIL>
- **Project**: Schedule Management System