package com.university.schedulemanagement.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;

/**
 * NhomLK Entity - Nhóm liên kết
 */
@Entity
@Table(name = "NHOM_LK")
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class NhomLK {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_NHOM_LK")
    private Long idNhomLk;

    @Column(name = "TEN_NHOM_LK", nullable = false, length = 100)
    private String tenNhomLk;

    @Column(name = "MO_TA", length = 250)
    private String moTa;

    @OneToMany(mappedBy = "nhomLk", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<MonHoc> monHocList;
}
