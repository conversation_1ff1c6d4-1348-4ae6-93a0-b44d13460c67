package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * VaiTroRepository - Repository cho Vai trò
 */
@Repository
public interface VaiTroRepository extends JpaRepository<VaiTro, Long> {

    Optional<VaiTro> findByTenVaiTro(String tenVaiTro);

    @Query("SELECT vt FROM VaiTro vt WHERE " +
            "LOWER(vt.tenVaiTro) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    List<VaiTro> findByKeyword(@Param("keyword") String keyword);
}
