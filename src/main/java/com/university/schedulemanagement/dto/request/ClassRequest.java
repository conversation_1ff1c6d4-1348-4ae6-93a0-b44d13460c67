package com.university.schedulemanagement.dto.request;

import javax.validation.constraints.*;
import lombok.Data;

/**
 * ClassRequest - DTO tạo/cập nhật lớp học
 */
@Data
public class ClassRequest {

    @NotBlank(message = "Mã lớp không được để trống")
    private String maLop;

    @NotBlank(message = "Tên lớp không được để trống")
    private String tenLop;

    @NotNull(message = "<PERSON><PERSON>i chọn ngành học")
    private Long idNganh;

    @NotNull(message = "<PERSON><PERSON><PERSON> chọn hệ đào tạo")
    private Long idHeDaoTao;

    @Min(value = 0, message = "<PERSON><PERSON> số không được âm")
    private Integer siSo = 0;
}
