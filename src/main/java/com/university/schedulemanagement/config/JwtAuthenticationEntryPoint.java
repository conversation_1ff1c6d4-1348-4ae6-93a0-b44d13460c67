package com.university.schedulemanagement.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * JwtAuthenticationEntryPoint - Xử lý lỗi xác thực
 */
@Component
@Slf4j
public class JwtAuthenticationEntryPoint implements org.springframework.security.web.AuthenticationEntryPoint {

    @Override
    public void commence(javax.servlet.http.HttpServletRequest request,
                         javax.servlet.http.HttpServletResponse response,
                         org.springframework.security.core.AuthenticationException authException)
            throws java.io.IOException {

        log.error("Responding with unauthorized error. Message - {}", authException.getMessage());

        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(javax.servlet.http.HttpServletResponse.SC_UNAUTHORIZED);

        String jsonResponse = String.format(
            "{\n" +
            "    \"success\": false,\n" +
            "    \"message\": \"Bạn cần đăng nhập để truy cập tài nguyên này\",\n" +
            "    \"data\": null,\n" +
            "    \"timestamp\": \"%s\"\n" +
            "}",
            java.time.LocalDateTime.now()
        );

        response.getWriter().write(jsonResponse);
    }
}
