package com.university.schedulemanagement.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;

/**
 * LoaiMonHoc Entity - Loại môn học
 */
@Entity
@Table(name = "LOAI_MON_HOC")
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class LoaiMonHoc {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_LOAI_MON_HOC")
    private Long idLoaiMonHoc;

    @Column(name = "TEN_LOAI", nullable = false, length = 100)
    private String tenLoai;

    @Column(name = "MO_TA", length = 250)
    private String moTa;

    @OneToMany(mappedBy = "loaiMonHoc", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<MonHoc> monHocList;
}
