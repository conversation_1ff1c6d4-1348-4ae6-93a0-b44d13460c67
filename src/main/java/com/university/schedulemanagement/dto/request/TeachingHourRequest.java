package com.university.schedulemanagement.dto.request;

import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * TeachingHourRequest - DTO request tính giờ giảng
 */
@Data
public class TeachingHourRequest {

    @NotNull(message = "<PERSON><PERSON><PERSON> chọn giảng viên")
    private Long teacherId;

    @NotNull(message = "<PERSON><PERSON><PERSON> chọn học kỳ")
    private Long semesterId;

    private Integer year;

    private Integer month;
}
