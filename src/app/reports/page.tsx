'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  FileText, 
  ArrowLeft,
  Download,
  Calendar,
  FileSpreadsheet,
  FilePdf,
  Users,
  BookOpen
} from 'lucide-react'

export default function ReportsPage() {
  const [loading, setLoading] = useState(false)
  const [reportParams, setReportParams] = useState({
    startDate: '',
    endDate: '',
    reportType: 'schedule'
  })
  const router = useRouter()

  useEffect(() => {
    // Kiểm tra token
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    // Set default dates (current month)
    const now = new Date()
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1)
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0)
    
    setReportParams({
      ...reportParams,
      startDate: firstDay.toISOString().split('T')[0],
      endDate: lastDay.toISOString().split('T')[0]
    })
  }, [router])

  const handleExportExcel = async (reportType: string) => {
    setLoading(true)
    try {
      const token = localStorage.getItem('token')
      const params = new URLSearchParams({
        startDate: reportParams.startDate,
        endDate: reportParams.endDate,
        type: reportType
      })

      const response = await fetch(`http://localhost:8080/api/reports/excel?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `bao-cao-${reportType}-${reportParams.startDate}-${reportParams.endDate}.xlsx`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        alert('Có lỗi xảy ra khi xuất báo cáo')
      }
    } catch (error) {
      console.error('Error exporting report:', error)
      alert('Có lỗi xảy ra khi xuất báo cáo')
    } finally {
      setLoading(false)
    }
  }

  const handleExportPDF = async (reportType: string) => {
    setLoading(true)
    try {
      const token = localStorage.getItem('token')
      const params = new URLSearchParams({
        startDate: reportParams.startDate,
        endDate: reportParams.endDate,
        type: reportType
      })

      const response = await fetch(`http://localhost:8080/api/reports/pdf?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `bao-cao-${reportType}-${reportParams.startDate}-${reportParams.endDate}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        alert('Có lỗi xảy ra khi xuất báo cáo')
      }
    } catch (error) {
      console.error('Error exporting report:', error)
      alert('Có lỗi xảy ra khi xuất báo cáo')
    } finally {
      setLoading(false)
    }
  }

  const reportTypes = [
    {
      id: 'schedule',
      title: 'Báo cáo lịch giảng',
      description: 'Xuất báo cáo chi tiết về lịch giảng dạy trong khoảng thời gian',
      icon: Calendar,
      color: 'bg-blue-500'
    },
    {
      id: 'teaching-hours',
      title: 'Báo cáo giờ giảng',
      description: 'Xuất báo cáo tổng hợp giờ giảng dạy và giờ quy đổi',
      icon: BookOpen,
      color: 'bg-green-500'
    },
    {
      id: 'teacher-summary',
      title: 'Báo cáo tổng hợp giảng viên',
      description: 'Xuất báo cáo tổng hợp theo từng giảng viên',
      icon: Users,
      color: 'bg-purple-500'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => router.push('/dashboard')}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Quay lại
              </Button>
              <FileText className="h-6 w-6 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">
                Báo cáo và xuất dữ liệu
              </h1>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Date Range Filter */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Chọn khoảng thời gian</CardTitle>
            <CardDescription>
              Chọn khoảng thời gian để xuất báo cáo
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">Từ ngày</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={reportParams.startDate}
                  onChange={(e) => setReportParams({
                    ...reportParams,
                    startDate: e.target.value
                  })}
                />
              </div>
              <div>
                <Label htmlFor="endDate">Đến ngày</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={reportParams.endDate}
                  onChange={(e) => setReportParams({
                    ...reportParams,
                    endDate: e.target.value
                  })}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Report Types */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {reportTypes.map((report) => (
            <Card key={report.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className={`w-12 h-12 ${report.color} rounded-lg flex items-center justify-center mb-4`}>
                  <report.icon className="h-6 w-6 text-white" />
                </div>
                <CardTitle className="text-lg">{report.title}</CardTitle>
                <CardDescription>{report.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button 
                    className="w-full" 
                    onClick={() => handleExportExcel(report.id)}
                    disabled={loading}
                  >
                    <FileSpreadsheet className="h-4 w-4 mr-2" />
                    Xuất Excel
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => handleExportPDF(report.id)}
                    disabled={loading}
                  >
                    <FilePdf className="h-4 w-4 mr-2" />
                    Xuất PDF
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Báo cáo nhanh</CardTitle>
            <CardDescription>
              Các báo cáo thường dùng với thời gian được thiết lập sẵn
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button 
                variant="outline" 
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => {
                  const now = new Date()
                  const firstDay = new Date(now.getFullYear(), now.getMonth(), 1)
                  const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0)
                  setReportParams({
                    ...reportParams,
                    startDate: firstDay.toISOString().split('T')[0],
                    endDate: lastDay.toISOString().split('T')[0]
                  })
                }}
              >
                <Calendar className="h-5 w-5 mb-2" />
                <span className="text-sm">Tháng này</span>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => {
                  const now = new Date()
                  const firstDay = new Date(now.getFullYear(), now.getMonth() - 1, 1)
                  const lastDay = new Date(now.getFullYear(), now.getMonth(), 0)
                  setReportParams({
                    ...reportParams,
                    startDate: firstDay.toISOString().split('T')[0],
                    endDate: lastDay.toISOString().split('T')[0]
                  })
                }}
              >
                <Calendar className="h-5 w-5 mb-2" />
                <span className="text-sm">Tháng trước</span>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => {
                  const now = new Date()
                  const firstDay = new Date(now.getFullYear(), 0, 1)
                  const lastDay = new Date(now.getFullYear(), 11, 31)
                  setReportParams({
                    ...reportParams,
                    startDate: firstDay.toISOString().split('T')[0],
                    endDate: lastDay.toISOString().split('T')[0]
                  })
                }}
              >
                <Calendar className="h-5 w-5 mb-2" />
                <span className="text-sm">Năm này</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
