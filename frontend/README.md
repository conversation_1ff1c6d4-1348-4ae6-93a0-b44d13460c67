# H<PERSON> thống quản lý lịch giảng - Frontend

Giao diện người dùng cho hệ thống quản lý lịch giảng được xây dựng bằng Next.js 15 và shadcn/ui.

## 🚀 Tính năng

### 🔐 <PERSON><PERSON><PERSON> thực và phân quyền
- <PERSON><PERSON><PERSON> nhập với mã cán bộ và mật khẩu
- <PERSON><PERSON> quyền theo vai trò (Admin, Trưởng khoa, Giảng viên)
- Tự động chuyển hướng dựa trên trạng thái đăng nhập

### 📊 Dashboard
- Tổng quan thống kê giờ giảng
- Thông tin cá nhân và vai trò
- T<PERSON>y cập nhanh các chức năng chính

### 📅 Quản lý lịch giảng
- Xem danh sách lịch giảng
- T<PERSON><PERSON> kiếm theo môn họ<PERSON>, <PERSON><PERSON><PERSON>, gi<PERSON><PERSON> viên
- <PERSON><PERSON><PERSON> thị trạng thái phê duyệt
- <PERSON><PERSON><PERSON><PERSON> tin chi tiết: <PERSON><PERSON><PERSON><PERSON> họ<PERSON>, b<PERSON><PERSON><PERSON> họ<PERSON>, <PERSON><PERSON><PERSON> thức

### ⏰ Quản lý giờ giảng
- Xem giờ giảng theo tháng/năm
- Tính toán giờ quy đổi với hệ số
- Thống kê tổng hợp
- Xuất báo cáo Excel

### 📈 Báo cáo
- Báo cáo lịch giảng
- Báo cáo giờ giảng dạy
- Báo cáo tổng hợp giảng viên
- Xuất file Excel và PDF
- Chọn khoảng thời gian tùy chỉnh

### ⚙️ Cài đặt
- Cập nhật thông tin cá nhân
- Đổi mật khẩu
- Bảo mật với hiển thị/ẩn mật khẩu

## 🛠️ Công nghệ sử dụng

- **Framework**: Next.js 15 với App Router
- **UI Library**: shadcn/ui
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Language**: TypeScript
- **HTTP Client**: Fetch API

## 📋 Yêu cầu hệ thống

- Node.js >= 18.18.0
- npm hoặc yarn
- Backend API đang chạy trên port 8080

## 🚀 Cài đặt và chạy

### 1. Cài đặt dependencies
```bash
npm install
```

### 2. Chạy development server
```bash
npm run dev
```

Ứng dụng sẽ chạy tại: http://localhost:3000

### 3. Build cho production
```bash
npm run build
npm start
```

## 📁 Cấu trúc thư mục

```
frontend/
├── src/
│   ├── app/                    # App Router pages
│   │   ├── dashboard/          # Trang dashboard
│   │   ├── login/              # Trang đăng nhập
│   │   ├── schedules/          # Quản lý lịch giảng
│   │   ├── teaching-hours/     # Quản lý giờ giảng
│   │   ├── reports/            # Báo cáo
│   │   ├── settings/           # Cài đặt
│   │   └── page.tsx            # Trang chủ (redirect)
│   ├── components/
│   │   └── ui/                 # shadcn/ui components
│   └── lib/
│       └── utils.ts            # Utility functions
├── public/                     # Static files
├── package.json
└── README.md
```

## 🔗 API Endpoints

Frontend kết nối với các API endpoints sau:

### Authentication
- `POST /api/auth/login` - Đăng nhập
- `PUT /api/auth/update-profile` - Cập nhật thông tin
- `PUT /api/auth/change-password` - Đổi mật khẩu

### Schedules
- `GET /api/schedules` - Lấy danh sách lịch giảng
- `POST /api/schedules` - Tạo lịch giảng mới
- `PUT /api/schedules/{id}` - Cập nhật lịch giảng
- `DELETE /api/schedules/{id}` - Xóa lịch giảng

### Teaching Hours
- `GET /api/teaching-hours` - Lấy giờ giảng theo tháng/năm
- `GET /api/teaching-hours/export/excel` - Xuất Excel

### Reports
- `GET /api/reports/excel` - Xuất báo cáo Excel
- `GET /api/reports/pdf` - Xuất báo cáo PDF

## 👤 Tài khoản mặc định

Để test ứng dụng, sử dụng các tài khoản sau:

- **Admin**: `admin` / `123456`
- **Trưởng khoa**: `truongkhoa` / `123456`
- **Giảng viên**: `giangvien` / `123456`

## 🎨 UI Components

Sử dụng shadcn/ui components:
- Button, Input, Label
- Card, Alert
- Table (custom implementation)
- Icons từ Lucide React

## 📱 Responsive Design

- Mobile-first approach
- Responsive grid layouts
- Adaptive navigation
- Touch-friendly interfaces

## 🔒 Bảo mật

- JWT token authentication
- Automatic token validation
- Secure password handling
- Protected routes

## 🚧 Tính năng sắp tới

- [ ] Tạo/chỉnh sửa lịch giảng
- [ ] Notification system
- [ ] Dark mode
- [ ] Advanced filtering
- [ ] Real-time updates
- [ ] Mobile app

## 🐛 Báo lỗi

Nếu gặp lỗi, vui lòng tạo issue với thông tin:
- Mô tả lỗi
- Các bước tái hiện
- Screenshots (nếu có)
- Thông tin browser/device

## 📄 License

MIT License - xem file LICENSE để biết thêm chi tiết.
