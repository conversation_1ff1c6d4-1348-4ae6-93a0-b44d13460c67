package com.university.schedulemanagement.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * UserPrincipal - Principal user cho Spring Security
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPrincipal implements org.springframework.security.core.userdetails.UserDetails {

    private Long id;
    private String maCanBo;
    private String ten;
    private String matKhau;
    private String role;
    private Boolean trangThai;

    @Override
    public Collection<? extends org.springframework.security.core.GrantedAuthority> getAuthorities() {
        return Arrays.asList(new org.springframework.security.core.authority.SimpleGrantedAuthority("ROLE_" + role));
    }

    @Override
    public String getPassword() {
        return matKhau;
    }

    @Override
    public String getUsername() {
        return maCanBo;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return trangThai;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return trangThai;
    }

    public static UserPrincipal create(com.university.schedulemanagement.entity.CanBo canBo) {
        String role = "GIANG_VIEN"; // Default role
        if (canBo.getVaiTro() != null) {
            String tenVaiTro = canBo.getVaiTro().getTenVaiTro().toUpperCase().trim();
            switch (tenVaiTro) {
                case "ADMIN":
                case "QU?N TR? VI?N":
                case "QUAN TRI VIEN":
                    role = "ADMIN";
                    break;
                case "TRUONG KHOA":
                case "TR??NG KHOA":
                case "TRUONG_KHOA":
                    role = "TRUONG_KHOA";
                    break;
                case "GIANG VIEN":
                case "GI?NG VI?N":
                case "GIANG_VIEN":
                    role = "GIANG_VIEN";
                    break;
                default:
                    role = "GIANG_VIEN";
                    break;
            }
        }

        return new UserPrincipal(
                canBo.getIdCanBo(),
                canBo.getMaCanBo(),
                canBo.getTen(),
                canBo.getMatKhau(),
                role,
                canBo.getTrangThai()
        );
    }
}
