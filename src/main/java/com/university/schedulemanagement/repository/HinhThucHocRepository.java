package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * HinhThucHocRepository - Repository cho <PERSON><PERSON>nh thức học
 */
@Repository
public interface HinhThucHocRepository extends JpaRepository<HinhThucHoc, Long> {

    Optional<HinhThucHoc> findByTenHinhThuc(String tenHinhThuc);

    List<HinhThucHoc> findAllByOrderByTenHinhThuc();
}
