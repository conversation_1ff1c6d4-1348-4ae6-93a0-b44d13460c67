package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * ThongKeGioGiangRepository - Repository cho Thống kê giờ giảng
 */
@Repository
public interface ThongKeGioGiangRepository extends JpaRepository<ThongKeGioGiang, Long> {

    Optional<ThongKeGioGiang> findByIdCanBoAndIdHocKy(Long idCanBo, Long idHocKy);

    List<ThongKeGioGiang> findByIdCanBo(Long idCanBo);

    List<ThongKeGioGiang> findByIdHocKy(Long idHocKy);

    @Query("SELECT tk FROM ThongKeGioGiang tk JOIN tk.hocKy hk JOIN hk.nienKhoa nk " +
            "WHERE tk.idCanBo = :idCanBo AND hk.ngayBatDau >= :fromDate AND hk.ngayKetThuc <= :toDate " +
            "ORDER BY hk.ngayBatDau")
    List<ThongKeGioGiang> findByCanBoAndPeriod(@Param("idCanBo") Long idCanBo,
                                               @Param("fromDate") LocalDate fromDate,
                                               @Param("toDate") LocalDate toDate);

    @Query("SELECT tk FROM ThongKeGioGiang tk JOIN tk.canBo cb JOIN cb.pbmm pbmm " +
            "WHERE pbmm.idPbmm = :idPbmm AND tk.idHocKy = :idHocKy")
    List<ThongKeGioGiang> findByKhoaAndHocKy(@Param("idPbmm") Long idPbmm, @Param("idHocKy") Long idHocKy);
}
