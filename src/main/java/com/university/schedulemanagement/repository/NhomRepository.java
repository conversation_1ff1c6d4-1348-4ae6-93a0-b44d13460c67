package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * NhomRepository - Repository cho Nhóm môn học
 */
@Repository
public interface NhomRepository extends JpaRepository<Nhom, Long> {

    List<Nhom> findByIdCanBo(Long idCanBo);

    List<Nhom> findByIdMonHoc(Long idMonHoc);

    @Query("SELECT n FROM Nhom n WHERE n.idCanBo = :idCanBo AND n.idMonHoc = :idMonHoc")
    List<Nhom> findByCanBoAndMonHoc(@Param("idCanBo") Long idCanBo, @Param("idMonHoc") Long idMonHoc);

    @Query("SELECT n FROM Nhom n WHERE " +
            "LOWER(n.tenNhom) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    Page<Nhom> findByKeyword(@Param("keyword") String keyword, Pageable pageable);

    @Query("SELECT COUNT(n) FROM Nhom n WHERE n.idCanBo = :idCanBo")
    Long countByCanBo(@Param("idCanBo") Long idCanBo);
}
