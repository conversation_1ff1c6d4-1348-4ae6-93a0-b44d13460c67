package com.university.schedulemanagement.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * ScheduleResponse - DTO response lịch giảng
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleResponse {

    private Long id;

    private String maMonHoc;

    private String tenMonHoc;

    private String tenGiangVien;

    private String maLop;

    private String tenLop;

    private String hinhThucHoc;

    private Integer thuHoc;

    private String thuHocText;

    private String tenBuoi;

    private LocalTime gioBatDau;

    private LocalTime gioKetThuc;

    private String tenPhong;

    private String tenCoSo;

    private Integer soTiet;

    private BigDecimal heSo;

    private BigDecimal soGioQuyDoi;

    private String nhomTh;

    private String tuanHoc;

    private String ghiChu;

    private Boolean trangThai;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}
