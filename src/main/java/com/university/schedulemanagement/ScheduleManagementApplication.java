package com.university.schedulemanagement;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main Application Class for Schedule Management System
 * Hệ thống quản lý lịch giảng và tổng hợp giờ giảng
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
public class ScheduleManagementApplication {

    public static void main(String[] args) {
        SpringApplication.run(ScheduleManagementApplication.class, args);
        System.out.println("===========================================");
        System.out.println("🎓 Schedule Management System Started! 🎓");
        System.out.println("📚 <PERSON><PERSON> thống quản lý lịch giảng đã khởi động!");
        System.out.println("🌐 Swagger UI: http://localhost:8080/api/swagger-ui.html");
        System.out.println("📊 API Docs: http://localhost:8080/api/api-docs");
        System.out.println("===========================================");
    }
}
