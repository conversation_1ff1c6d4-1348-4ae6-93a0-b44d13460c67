#!/bin/bash

# Đường dẫn gốc
BASE_DIR="src/main/java/com/university/schedulemanagement"
TEST_DIR="src/test/java/com/university/schedulemanagement"

# Tạo các thư mục chính
mkdir -p $BASE_DIR/{config,controller,dto/{request,response},entity,repository,service,serviceImpl,security,exception,util,constant}
mkdir -p src/main/resources/{static/{css,js,images},templates/email}
mkdir -p $TEST_DIR/{controller,service,repository}

# Tạo các file Java chính
touch $BASE_DIR/ScheduleManagementApplication.java

# Config
for file in WebConfig SecurityConfig JwtConfig SwaggerConfig; do
    touch $BASE_DIR/config/${file}.java
done

# Controllers
for file in AuthController ScheduleController TeachingHourController SubjectController ClassController TeacherController; do
    touch $BASE_DIR/controller/${file}.java
done

# DTO Requests
for file in LoginRequest ScheduleRequest TeachingHourRequest FilterRequest; do
    touch $BASE_DIR/dto/request/${file}.java
done

# DTO Responses
for file in LoginResponse ScheduleResponse TeachingHourResponse SubjectResponse ApiResponse; do
    touch $BASE_DIR/dto/response/${file}.java
done

# Entities
entities=(CanBo MonHoc Nhom HocKy NienKhoa LichGiang ThongKeGioGiang LopHoc NganhHoc PBMM VaiTro HinhThucHoc CoSo PhongHoc BuoiHoc HeDaoTao LoaiMonHoc NhomLK)
for entity in "${entities[@]}"; do
    touch $BASE_DIR/entity/${entity}.java
done

# Repositories
for entity in "${entities[@]}"; do
    touch $BASE_DIR/repository/${entity}Repository.java
done

# Services & Implementations
services=(Auth Schedule TeachingHour Subject Class Teacher ExcelExport Email)
for service in "${services[@]}"; do
    touch $BASE_DIR/service/${service}Service.java
    touch $BASE_DIR/serviceImpl/${service}ServiceImpl.java
done

# Security
for file in JwtAuthenticationEntryPoint JwtAuthenticationFilter JwtTokenProvider UserPrincipal; do
    touch $BASE_DIR/security/${file}.java
done

# Exceptions
for file in GlobalExceptionHandler ResourceNotFoundException BadRequestException UnauthorizedException; do
    touch $BASE_DIR/exception/${file}.java
done

# Utils
for file in DateUtils ExcelUtils ResponseUtils ValidationUtils; do
    touch $BASE_DIR/util/${file}.java
done

# Constants
for file in AppConstants MessageConstants SecurityConstants; do
    touch $BASE_DIR/constant/${file}.java
done

# Resources
touch src/main/resources/application.properties
touch src/main/resources/application-dev.properties
touch src/main/resources/application-prod.properties
touch src/main/resources/templates/email/schedule-notification.html

# Test Files
touch $TEST_DIR/ScheduleManagementApplicationTests.java
for file in AuthController ScheduleController TeachingHourController; do
    touch $TEST_DIR/controller/${file}Test.java
done
for file in AuthService ScheduleService TeachingHourService; do
    touch $TEST_DIR/service/${file}Test.java
done
for file in CanBoRepository MonHocRepository LichGiangRepository; do
    touch $TEST_DIR/repository/${file}Test.java
done

# Root files
touch pom.xml README.md .gitignore docker-compose.yml

echo "✅ Project structure created successfully!"
