package com.university.schedulemanagement.controller;

import com.university.schedulemanagement.dto.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * HealthController - Health check endpoints
 */
@RestController
@RequestMapping("/health")
@Tag(name = "Health", description = "Health check APIs")
@Slf4j
public class HealthController {

    @GetMapping
    @Operation(summary = "Health check", description = "Kiểm tra trạng thái hệ thống")
    public ResponseEntity<ApiResponse<Map<String, Object>>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", LocalDateTime.now());
        health.put("service", "Schedule Management System");
        health.put("version", "1.0.0");
        
        return ResponseEntity.ok(ApiResponse.success(health, "Hệ thống hoạt động bình thường"));
    }

    @GetMapping("/info")
    @Operation(summary = "System info", description = "Thông tin hệ thống")
    public ResponseEntity<ApiResponse<Map<String, Object>>> systemInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("name", "Schedule Management System");
        info.put("description", "Hệ thống quản lý lịch giảng và tổng hợp giờ giảng");
        info.put("version", "1.0.0");
        info.put("java.version", System.getProperty("java.version"));
        info.put("spring.version", "2.7.18");
        info.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(ApiResponse.success(info, "Thông tin hệ thống"));
    }
}
