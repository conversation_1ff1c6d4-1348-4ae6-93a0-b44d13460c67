package com.university.schedulemanagement.service;

import com.university.schedulemanagement.dto.request.*;
import com.university.schedulemanagement.dto.response.*;
import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;
/**
 * DashboardService - Service xử lý dashboard
 */
public interface DashboardService {

    /**
     * L<PERSON>y thống kê tổng quan
     */
    DashboardResponse getDashboardStats();

    /**
     * Lấy thống kê theo khoa
     */
    DashboardResponse getDashboardStatsByDepartment(Long departmentId);

    /**
     * L<PERSON>y thống kê giờ giảng theo tháng
     */
    List<MonthlyStatsResponse> getMonthlyTeachingHours(Long teacherId, int year);

    /**
     * Lấy top môn học có nhiều giờ nhất
     */
    List<SubjectStatsResponse> getTopSubjectsByHours(Long semesterId, int limit);
}
