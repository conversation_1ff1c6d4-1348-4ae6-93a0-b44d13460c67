# 📋 API Examples - <PERSON><PERSON> Chi Tiết

## 🔐 Authentication Examples

### 1. <PERSON><PERSON><PERSON>p Admin
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "maCanBo": "admin",
    "matKhau": "123456"
  }'
```

### 2. <PERSON><PERSON><PERSON>i<PERSON>
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "maCanBo": "giangvien",
    "matKhau": "123456"
  }'
```

## 📅 Schedule Management Examples

### 1. T<PERSON><PERSON>ch G<PERSON>ảng <PERSON>ới
```bash
curl -X POST http://localhost:8080/api/schedules \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "idCanBo": 3,
    "idMonHoc": 1,
    "idLop": 1,
    "idHocKy": 1,
    "idPhong": 1,
    "idBuoi": 1,
    "idHinhThuc": 1,
    "thuHoc": 2,
    "soTiet": 4,
    "heSo": 1.0,
    "nhomTh": "Nhóm A",
    "tuanHoc": "1-15",
    "ghiChu": "Môn Lập trình Java"
  }'
```

### 2. Lấy Lịch Giảng Theo Giảng Viên
```bash
curl -X GET "http://localhost:8080/api/schedules/teacher/3?semesterId=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. Kiểm Tra Phòng Học Khả Dụng
```bash
curl -X GET "http://localhost:8080/api/schedules/available-rooms?coSoId=1&loaiPhong=LT&thuHoc=2&buoiId=1&hocKyId=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## ⏰ Teaching Hours Examples

### 1. Lấy Giờ Giảng Cá Nhân
```bash
curl -X GET "http://localhost:8080/api/teaching-hours/personal?semesterId=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. Tính Toán Giờ Giảng
```bash
curl -X POST "http://localhost:8080/api/teaching-hours/calculate?teacherId=3&semesterId=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📊 Export Examples

### 1. Xuất Lịch Giảng Excel
```bash
curl -X GET "http://localhost:8080/api/schedules/export/personal?teacherId=3&semesterId=1" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -o "lich_giang_ca_nhan.xlsx"
```

## 🔍 Data Structure Examples

### ScheduleRequest
```json
{
  "idCanBo": 3,           // ID giảng viên
  "idMonHoc": 1,          // ID môn học
  "idLop": 1,             // ID lớp học
  "idHocKy": 1,           // ID học kỳ
  "idPhong": 1,           // ID phòng học
  "idBuoi": 1,            // ID buổi học (1: Sáng, 2: Chiều, 3: Tối)
  "idHinhThuc": 1,        // ID hình thức (1: LT, 2: TH)
  "thuHoc": 2,            // Thứ học (2-8: Thứ 2 - Chủ nhật)
  "soTiet": 4,            // Số tiết học
  "heSo": 1.0,            // Hệ số quy đổi
  "nhomTh": "Nhóm A",     // Nhóm thực hành (optional)
  "tuanHoc": "1-15",      // Các tuần học
  "ghiChu": "Ghi chú"     // Ghi chú (optional)
}
```

### ScheduleResponse
```json
{
  "id": 1,
  "maCanBo": "GV001",
  "tenCanBo": "Nguyễn Văn A",
  "tenKhoa": "Công nghệ thông tin",
  "maMonHoc": "IT001",
  "tenMonHoc": "Lập trình Java",
  "maLop": "IT2024A",
  "tenLop": "Công nghệ thông tin K24A",
  "tenHocKy": "Học kỳ 1 (2024-2025)",
  "maPhong": "P101",
  "tenPhong": "Phòng 101",
  "tenCoSo": "Cơ sở 1",
  "tenBuoi": "Sáng",
  "gioBatDau": "07:00",
  "gioKetThuc": "11:30",
  "tenHinhThuc": "Lý thuyết",
  "thuHoc": 2,
  "thuHocText": "Thứ 2",
  "soTiet": 4,
  "heSo": 1.0,
  "nhomTh": "Nhóm A",
  "tuanHoc": "1-15",
  "ghiChu": "Môn Lập trình Java",
  "trangThai": true,
  "createdAt": "2024-01-01T10:00:00",
  "updatedAt": "2024-01-01T10:00:00"
}
```

### TeachingHourResponse
```json
{
  "teacherId": 3,
  "maCanBo": "GV001",
  "tenCanBo": "Nguyễn Văn A",
  "tenKhoa": "Công nghệ thông tin",
  "semesterId": 1,
  "tenHocKy": "Học kỳ 1 (2024-2025)",
  "tongGioLt": 60.0,      // Tổng giờ lý thuyết
  "tongGioTh": 30.0,      // Tổng giờ thực hành
  "tongGioTong": 90.0,    // Tổng giờ
  "tongGioQuyDoi": 85.0,  // Tổng giờ quy đổi
  "ngayTinh": "2024-01-01T10:00:00",
  "lichGiangList": [...]  // Danh sách lịch giảng chi tiết
}
```

### LoginResponse
```json
{
  "accessToken": "eyJhbGciOiJIUzUxMiJ9...",
  "tokenType": "Bearer",
  "expiresIn": 1640995200000,
  "teacherInfo": {
    "id": 1,
    "maCanBo": "admin",
    "ten": "Quản trị viên",
    "email": "<EMAIL>",
    "sdt": "0123456789",
    "vaiTro": "ADMIN",
    "tenKhoa": "Công nghệ thông tin",
    "nu": false
  }
}
```

## 🎯 Common Use Cases

### 1. Workflow Tạo Lịch Giảng
```bash
# Bước 1: Đăng nhập
TOKEN=$(curl -s -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"maCanBo":"admin","matKhau":"123456"}' | \
  jq -r '.data.accessToken')

# Bước 2: Kiểm tra phòng học khả dụng
curl -X GET "http://localhost:8080/api/schedules/available-rooms?coSoId=1&loaiPhong=LT&thuHoc=2&buoiId=1&hocKyId=1" \
  -H "Authorization: Bearer $TOKEN"

# Bước 3: Tạo lịch giảng
curl -X POST http://localhost:8080/api/schedules \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "idCanBo": 3,
    "idMonHoc": 1,
    "idLop": 1,
    "idHocKy": 1,
    "idPhong": 1,
    "idBuoi": 1,
    "idHinhThuc": 1,
    "thuHoc": 2,
    "soTiet": 4,
    "heSo": 1.0,
    "tuanHoc": "1-15"
  }'
```

### 2. Workflow Tính Giờ Giảng
```bash
# Bước 1: Đăng nhập
TOKEN=$(curl -s -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"maCanBo":"truongkhoa","matKhau":"123456"}' | \
  jq -r '.data.accessToken')

# Bước 2: Tính toán giờ giảng cho tất cả giảng viên
curl -X POST "http://localhost:8080/api/teaching-hours/calculate-all?semesterId=1" \
  -H "Authorization: Bearer $TOKEN"

# Bước 3: Lấy báo cáo giờ giảng theo khoa
curl -X GET "http://localhost:8080/api/teaching-hours/department/1?semesterId=1" \
  -H "Authorization: Bearer $TOKEN"

# Bước 4: Xuất báo cáo Excel
curl -X GET "http://localhost:8080/api/teaching-hours/export?departmentId=1&semesterId=1" \
  -H "Authorization: Bearer $TOKEN" \
  -o "bao_cao_gio_giang.xlsx"
```

### 3. Workflow Giảng Viên Xem Lịch
```bash
# Bước 1: Đăng nhập
TOKEN=$(curl -s -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"maCanBo":"giangvien","matKhau":"123456"}' | \
  jq -r '.data.accessToken')

# Bước 2: Xem thông tin cá nhân
curl -X GET http://localhost:8080/api/auth/me \
  -H "Authorization: Bearer $TOKEN"

# Bước 3: Xem lịch giảng cá nhân
curl -X GET "http://localhost:8080/api/schedules/personal?semesterId=1" \
  -H "Authorization: Bearer $TOKEN"

# Bước 4: Xem giờ giảng cá nhân
curl -X GET "http://localhost:8080/api/teaching-hours/personal?semesterId=1" \
  -H "Authorization: Bearer $TOKEN"

# Bước 5: Xuất lịch giảng cá nhân
curl -X GET "http://localhost:8080/api/schedules/export/personal?semesterId=1" \
  -H "Authorization: Bearer $TOKEN" \
  -o "lich_giang_ca_nhan.xlsx"
```

## 🔧 Testing với Postman

### 1. Import Collection
Tạo file `Schedule_Management_API.postman_collection.json`:

```json
{
  "info": {
    "name": "Schedule Management API",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{access_token}}",
        "type": "string"
      }
    ]
  },
  "variable": [
    {
      "key": "base_url",
      "value": "http://localhost:8080/api"
    },
    {
      "key": "access_token",
      "value": ""
    }
  ]
}
```

### 2. Environment Variables
```json
{
  "name": "Schedule Management Local",
  "values": [
    {
      "key": "base_url",
      "value": "http://localhost:8080/api",
      "enabled": true
    },
    {
      "key": "access_token",
      "value": "",
      "enabled": true
    }
  ]
}
```

## 🐛 Troubleshooting

### 1. Lỗi 401 Unauthorized
```json
{
  "success": false,
  "message": "Bạn cần đăng nhập để truy cập tài nguyên này",
  "data": null
}
```
**Giải pháp**: Kiểm tra token có hợp lệ và chưa hết hạn

### 2. Lỗi 403 Forbidden
```json
{
  "success": false,
  "message": "Bạn không có quyền truy cập",
  "data": null
}
```
**Giải pháp**: Kiểm tra quyền của user có phù hợp với endpoint

### 3. Lỗi 400 Bad Request
```json
{
  "success": false,
  "message": "Dữ liệu không hợp lệ",
  "data": null
}
```
**Giải pháp**: Kiểm tra format và validation của request body

### 4. Lỗi 500 Internal Server Error
```json
{
  "success": false,
  "message": "Lỗi hệ thống",
  "data": null
}
```
**Giải pháp**: Kiểm tra log server và database connection
