package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * CoSoRepository - Repository cho Cơ sở
 */
@Repository
public interface CoSoRepository extends JpaRepository<CoSo, Long> {

    Optional<CoSo> findByMaCoSo(String maCoSo);

    @Query("SELECT cs FROM CoSo cs WHERE " +
            "(LOWER(cs.tenCoSo) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "LOWER(cs.maCoSo) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    List<CoSo> findByKeyword(@Param("keyword") String keyword);
}
