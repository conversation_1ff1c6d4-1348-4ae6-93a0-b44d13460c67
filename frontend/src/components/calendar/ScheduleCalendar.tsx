'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  ChevronLeft, 
  ChevronRight, 
  Calendar,
  Clock,
  MapPin,
  User,
  BookOpen
} from 'lucide-react'

interface ScheduleEvent {
  id: string
  title: string
  teacher: string
  room: string
  class: string
  time: string
  date: string
  color: string
  type: 'theory' | 'practice' | 'discussion'
}

interface CalendarProps {
  events?: ScheduleEvent[]
  onDateSelect?: (date: Date) => void
  onEventClick?: (event: ScheduleEvent) => void
}

export default function ScheduleCalendar({ events = [], onDateSelect, onEventClick }: CalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [viewMode, setViewMode] = useState<'month' | 'week'>('month')

  // Mock data for demo
  const mockEvents: ScheduleEvent[] = [
    {
      id: '1',
      title: '<PERSON>ậ<PERSON> tr<PERSON>nh <PERSON>',
      teacher: '<PERSON><PERSON><PERSON><PERSON>',
      room: 'A101',
      class: 'CNTT01',
      time: '07:00-09:00',
      date: '2024-01-15',
      color: 'bg-blue-500',
      type: 'theory'
    },
    {
      id: '2',
      title: 'Cơ sở dữ liệu',
      teacher: 'Trần Thị B',
      room: 'B201',
      class: 'CNTT02',
      time: '09:00-11:00',
      date: '2024-01-15',
      color: 'bg-green-500',
      type: 'practice'
    },
    {
      id: '3',
      title: 'Mạng máy tính',
      teacher: 'Lê Văn C',
      room: 'A102',
      class: 'CNTT03',
      time: '13:00-15:00',
      date: '2024-01-16',
      color: 'bg-purple-500',
      type: 'theory'
    },
    {
      id: '4',
      title: 'Phân tích thiết kế',
      teacher: 'Phạm Thị D',
      room: 'B202',
      class: 'KTPM01',
      time: '15:00-17:00',
      date: '2024-01-16',
      color: 'bg-orange-500',
      type: 'discussion'
    }
  ]

  const allEvents = events.length > 0 ? events : mockEvents

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day))
    }
    
    return days
  }

  const getEventsForDate = (date: Date | null) => {
    if (!date) return []
    const dateStr = date.toISOString().split('T')[0]
    return allEvents.filter(event => event.date === dateStr)
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const formatMonth = (date: Date) => {
    return date.toLocaleDateString('vi-VN', { 
      month: 'long', 
      year: 'numeric' 
    })
  }

  const isToday = (date: Date | null) => {
    if (!date) return false
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  const isSelected = (date: Date | null) => {
    if (!date || !selectedDate) return false
    return date.toDateString() === selectedDate.toDateString()
  }

  const handleDateClick = (date: Date | null) => {
    if (!date) return
    setSelectedDate(date)
    onDateSelect?.(date)
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'theory':
        return <BookOpen className="h-3 w-3" />
      case 'practice':
        return <Clock className="h-3 w-3" />
      case 'discussion':
        return <User className="h-3 w-3" />
      default:
        return <Calendar className="h-3 w-3" />
    }
  }

  const days = getDaysInMonth(currentDate)
  const weekDays = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7']

  return (
    <div className="space-y-4">
      {/* Calendar Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Lịch giảng toàn trường
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'month' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('month')}
              >
                Tháng
              </Button>
              <Button
                variant={viewMode === 'week' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('week')}
              >
                Tuần
              </Button>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm" onClick={() => navigateMonth('prev')}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h3 className="text-lg font-semibold capitalize">
                {formatMonth(currentDate)}
              </h3>
              <Button variant="outline" size="sm" onClick={() => navigateMonth('next')}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentDate(new Date())}
            >
              Hôm nay
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1">
            {/* Week day headers */}
            {weekDays.map(day => (
              <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                {day}
              </div>
            ))}
            
            {/* Calendar days */}
            {days.map((date, index) => {
              const dayEvents = getEventsForDate(date)
              
              return (
                <div
                  key={index}
                  className={`
                    min-h-[100px] p-1 border border-gray-200 cursor-pointer transition-colors
                    ${date ? 'hover:bg-gray-50' : ''}
                    ${isToday(date) ? 'bg-blue-50 border-blue-200' : ''}
                    ${isSelected(date) ? 'bg-blue-100 border-blue-300' : ''}
                    ${!date ? 'bg-gray-50' : ''}
                  `}
                  onClick={() => handleDateClick(date)}
                >
                  {date && (
                    <>
                      <div className={`
                        text-sm font-medium mb-1
                        ${isToday(date) ? 'text-blue-600' : 'text-gray-900'}
                      `}>
                        {date.getDate()}
                      </div>
                      
                      {/* Events for this day */}
                      <div className="space-y-1">
                        {dayEvents.slice(0, 3).map(event => (
                          <div
                            key={event.id}
                            className={`
                              ${event.color} text-white text-xs p-1 rounded cursor-pointer
                              hover:opacity-80 transition-opacity
                            `}
                            onClick={(e) => {
                              e.stopPropagation()
                              onEventClick?.(event)
                            }}
                          >
                            <div className="flex items-center space-x-1">
                              {getTypeIcon(event.type)}
                              <span className="truncate">{event.title}</span>
                            </div>
                            <div className="text-xs opacity-90">
                              {event.time}
                            </div>
                          </div>
                        ))}
                        
                        {dayEvents.length > 3 && (
                          <div className="text-xs text-gray-500 text-center">
                            +{dayEvents.length - 3} khác
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Selected Date Events */}
      {selectedDate && (
        <Card>
          <CardHeader>
            <CardTitle>
              Lịch giảng ngày {selectedDate.toLocaleDateString('vi-VN')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {getEventsForDate(selectedDate).length === 0 ? (
                <p className="text-gray-500 text-center py-4">
                  Không có lịch giảng nào trong ngày này
                </p>
              ) : (
                getEventsForDate(selectedDate).map(event => (
                  <div
                    key={event.id}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => onEventClick?.(event)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-semibold text-lg">{event.title}</h4>
                        <div className="mt-2 space-y-1 text-sm text-gray-600">
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2" />
                            <span>GV: {event.teacher}</span>
                          </div>
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-2" />
                            <span>Phòng: {event.room}</span>
                          </div>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-2" />
                            <span>Thời gian: {event.time}</span>
                          </div>
                        </div>
                      </div>
                      <div className={`${event.color} text-white px-3 py-1 rounded-full text-sm`}>
                        {event.class}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
