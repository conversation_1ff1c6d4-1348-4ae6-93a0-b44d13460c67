'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  ArrowLeft,
  Save,
  Calendar,
  Clock,
  MapPin,
  User,
  BookOpen,
  Users
} from 'lucide-react'

interface FormData {
  idMonHoc: string
  idLop: string
  idCanBo: string
  idPhong: string
  idBuoi: string
  idHinhThuc: string
  ngayBatDau: string
  ngayKetThuc: string
  soTiet: number
  ghiChu: string
}

interface Option {
  id: string
  name: string
}

export default function CreateSchedulePage() {
  const [formData, setFormData] = useState<FormData>({
    idMonHoc: '',
    idLop: '',
    idCanBo: '',
    idPhong: '',
    idBuoi: '',
    idHinhThuc: '',
    ngayBatDau: '',
    ngayKetThuc: '',
    soTiet: 0,
    ghiChu: ''
  })

  const [options, setOptions] = useState({
    subjects: [] as Option[],
    classes: [] as Option[],
    teachers: [] as Option[],
    rooms: [] as Option[],
    sessions: [] as Option[],
    formats: [] as Option[]
  })

  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [messageType, setMessageType] = useState<'success' | 'error'>('success')
  const router = useRouter()

  useEffect(() => {
    // Kiểm tra token
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    fetchOptions()
  }, [router])

  const fetchOptions = async () => {
    try {
      const token = localStorage.getItem('token')
      
      // Mock data for demo - trong thực tế sẽ gọi API
      setOptions({
        subjects: [
          { id: '1', name: 'Lập trình Java' },
          { id: '2', name: 'Cơ sở dữ liệu' },
          { id: '3', name: 'Mạng máy tính' },
          { id: '4', name: 'Phân tích thiết kế hệ thống' }
        ],
        classes: [
          { id: '1', name: 'CNTT01' },
          { id: '2', name: 'CNTT02' },
          { id: '3', name: 'CNTT03' },
          { id: '4', name: 'KTPM01' }
        ],
        teachers: [
          { id: '1', name: 'Nguyễn Văn A' },
          { id: '2', name: 'Trần Thị B' },
          { id: '3', name: 'Lê Văn C' },
          { id: '4', name: 'Phạm Thị D' }
        ],
        rooms: [
          { id: '1', name: 'A101' },
          { id: '2', name: 'A102' },
          { id: '3', name: 'B201' },
          { id: '4', name: 'B202' }
        ],
        sessions: [
          { id: '1', name: 'Sáng (7:00-11:00)' },
          { id: '2', name: 'Chiều (13:00-17:00)' },
          { id: '3', name: 'Tối (18:00-21:00)' }
        ],
        formats: [
          { id: '1', name: 'Lý thuyết' },
          { id: '2', name: 'Thực hành' },
          { id: '3', name: 'Thảo luận' }
        ]
      })
    } catch (error) {
      console.error('Error fetching options:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setMessage('')

    try {
      const token = localStorage.getItem('token')
      const response = await fetch('http://localhost:8080/api/schedules', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      const data = await response.json()

      if (data.success) {
        setMessage('Tạo lịch giảng thành công!')
        setMessageType('success')
        setTimeout(() => {
          router.push('/schedules')
        }, 2000)
      } else {
        setMessage(data.message || 'Có lỗi xảy ra khi tạo lịch giảng')
        setMessageType('error')
      }
    } catch (error) {
      console.error('Error creating schedule:', error)
      setMessage('Có lỗi xảy ra khi tạo lịch giảng')
      setMessageType('error')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: name === 'soTiet' ? parseInt(value) || 0 : value
    })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => router.push('/schedules')}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Quay lại
              </Button>
              <Calendar className="h-6 w-6 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">
                Tạo lịch giảng mới
              </h1>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {message && (
          <Alert variant={messageType === 'error' ? 'destructive' : 'default'} className="mb-6">
            <AlertDescription>{message}</AlertDescription>
          </Alert>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Thông tin lịch giảng</CardTitle>
            <CardDescription>
              Điền đầy đủ thông tin để tạo lịch giảng mới
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Row 1: Môn học và Lớp */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="idMonHoc" className="flex items-center">
                    <BookOpen className="h-4 w-4 mr-2" />
                    Môn học
                  </Label>
                  <select
                    id="idMonHoc"
                    name="idMonHoc"
                    value={formData.idMonHoc}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Chọn môn học</option>
                    {options.subjects.map(subject => (
                      <option key={subject.id} value={subject.id}>
                        {subject.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="idLop" className="flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    Lớp
                  </Label>
                  <select
                    id="idLop"
                    name="idLop"
                    value={formData.idLop}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Chọn lớp</option>
                    {options.classes.map(cls => (
                      <option key={cls.id} value={cls.id}>
                        {cls.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Row 2: Giảng viên và Phòng học */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="idCanBo" className="flex items-center">
                    <User className="h-4 w-4 mr-2" />
                    Giảng viên
                  </Label>
                  <select
                    id="idCanBo"
                    name="idCanBo"
                    value={formData.idCanBo}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Chọn giảng viên</option>
                    {options.teachers.map(teacher => (
                      <option key={teacher.id} value={teacher.id}>
                        {teacher.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="idPhong" className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2" />
                    Phòng học
                  </Label>
                  <select
                    id="idPhong"
                    name="idPhong"
                    value={formData.idPhong}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Chọn phòng học</option>
                    {options.rooms.map(room => (
                      <option key={room.id} value={room.id}>
                        {room.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Row 3: Buổi học và Hình thức */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="idBuoi" className="flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    Buổi học
                  </Label>
                  <select
                    id="idBuoi"
                    name="idBuoi"
                    value={formData.idBuoi}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Chọn buổi học</option>
                    {options.sessions.map(session => (
                      <option key={session.id} value={session.id}>
                        {session.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="idHinhThuc">Hình thức</Label>
                  <select
                    id="idHinhThuc"
                    name="idHinhThuc"
                    value={formData.idHinhThuc}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Chọn hình thức</option>
                    {options.formats.map(format => (
                      <option key={format.id} value={format.id}>
                        {format.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Row 4: Thời gian và Số tiết */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="ngayBatDau">Ngày bắt đầu</Label>
                  <Input
                    id="ngayBatDau"
                    name="ngayBatDau"
                    type="date"
                    value={formData.ngayBatDau}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ngayKetThuc">Ngày kết thúc</Label>
                  <Input
                    id="ngayKetThuc"
                    name="ngayKetThuc"
                    type="date"
                    value={formData.ngayKetThuc}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="soTiet">Số tiết</Label>
                  <Input
                    id="soTiet"
                    name="soTiet"
                    type="number"
                    min="1"
                    value={formData.soTiet}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>

              {/* Row 5: Ghi chú */}
              <div className="space-y-2">
                <Label htmlFor="ghiChu">Ghi chú</Label>
                <textarea
                  id="ghiChu"
                  name="ghiChu"
                  value={formData.ghiChu}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Nhập ghi chú (tùy chọn)"
                />
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push('/schedules')}
                >
                  Hủy
                </Button>
                <Button type="submit" disabled={loading}>
                  <Save className="h-4 w-4 mr-2" />
                  {loading ? 'Đang tạo...' : 'Tạo lịch giảng'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
