package com.university.schedulemanagement.service;

import com.university.schedulemanagement.dto.request.*;
import com.university.schedulemanagement.dto.response.*;
import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

/**
 * SubjectService - Service xử lý môn học
 */
public interface SubjectService {

    MonHoc createSubject(SubjectRequest request);

    MonHoc updateSubject(Long id, SubjectRequest request);

    void deleteSubject(Long id);

    MonHoc getSubjectById(Long id);

    Page<MonHoc> getAllSubjects(Pageable pageable);

    Page<MonHoc> searchSubjects(String keyword, Pageable pageable);

    List<MonHoc> getSubjectsByDepartment(Long departmentId);

    List<MonHoc> getSubjectsByEducationLevel(Long educationLevelId);

    List<MonHoc> getSubjectsByDepartmentAndEducationLevel(Long departmentId, Long educationLevelId);
}
