package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * NganhHocRepository - Repository cho Ng<PERSON>nh học
 */
@Repository
public interface NganhHocRepository extends JpaRepository<NganhHoc, Long> {

    Optional<NganhHoc> findByMaNganh(String maNganh);

    List<NganhHoc> findByIdPbmm(Long idPbmm);

    @Query("SELECT nh FROM NganhHoc nh WHERE " +
            "(LOWER(nh.tenNganh) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "LOWER(nh.maNganh) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<NganhHoc> findByKeyword(@Param("keyword") String keyword, Pageable pageable);
}
