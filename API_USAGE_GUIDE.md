# 📚 Hướng Dẫn Sử Dụng API - Hệ Thống Quản Lý Lịch Giảng

## 🌐 Thông Tin Cơ Bản

- **Base URL**: `http://localhost:8080/api`
- **Swagger UI**: `http://localhost:8080/api/swagger-ui.html`
- **API Docs**: `http://localhost:8080/api/api-docs`
- **Authentication**: JWT Bearer Token

## 🔐 <PERSON><PERSON><PERSON> (Authentication)

### 1. Đ<PERSON><PERSON>
```http
POST /api/auth/login
Content-Type: application/json

{
  "maCanBo": "admin",
  "matKhau": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Đăng nhập thành công",
  "data": {
    "accessToken": "eyJhbGciOiJIUzUxMiJ9...",
    "tokenType": "Bearer",
    "expiresIn": 1640995200000,
    "teacherInfo": {
      "id": 1,
      "maCanBo": "admin",
      "ten": "Quản trị viên",
      "email": "<EMAIL>",
      "sdt": "0123456789",
      "vaiTro": "ADMIN",
      "tenKhoa": "Công nghệ thông tin",
      "nu": false
    }
  }
}
```

### 2. Tài Khoản Mặc Định
- **Admin**: `admin` / `123456`
- **Trưởng Khoa**: `truongkhoa` / `123456`
- **Giảng Viên**: `giangvien` / `123456`

### 3. Sử Dụng Token
Thêm header Authorization vào tất cả các request cần xác thực:
```http
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```

## 👤 Quản Lý Người Dùng

### Lấy Thông Tin Người Dùng Hiện Tại
```http
GET /api/auth/me
Authorization: Bearer {token}
```

### Đổi Mật Khẩu
```http
POST /api/auth/change-password
Authorization: Bearer {token}
Content-Type: application/json

{
  "matKhauCu": "123456",
  "matKhauMoi": "newpassword",
  "xacNhanMatKhau": "newpassword"
}
```

### Đăng Xuất
```http
POST /api/auth/logout
Authorization: Bearer {token}
```

## 📅 Quản Lý Lịch Giảng

### 1. Tạo Lịch Giảng Mới
**Quyền**: ADMIN, TRUONG_KHOA
```http
POST /api/schedules
Authorization: Bearer {token}
Content-Type: application/json

{
  "idCanBo": 1,
  "idMonHoc": 1,
  "idLop": 1,
  "idHocKy": 1,
  "idPhong": 1,
  "idBuoi": 1,
  "idHinhThuc": 1,
  "thuHoc": 2,
  "soTiet": 4,
  "heSo": 1.0,
  "nhomTh": "Nhóm 1",
  "tuanHoc": "1-15",
  "ghiChu": "Ghi chú"
}
```

### 2. Lấy Lịch Giảng Theo Giảng Viên
```http
GET /api/schedules/teacher/{teacherId}?semesterId={semesterId}
Authorization: Bearer {token}
```

### 3. Lấy Lịch Giảng Theo Lớp
**Quyền**: ADMIN, TRUONG_KHOA
```http
GET /api/schedules/class/{classId}?semesterId={semesterId}
Authorization: Bearer {token}
```

### 4. Lấy Lịch Giảng Theo Học Kỳ (Phân Trang)
**Quyền**: ADMIN, TRUONG_KHOA
```http
GET /api/schedules/semester/{semesterId}?page=0&size=10&sortBy=createdAt&sortDir=desc
Authorization: Bearer {token}
```

### 5. Cập Nhật Lịch Giảng
**Quyền**: ADMIN, TRUONG_KHOA
```http
PUT /api/schedules/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "idCanBo": 1,
  "idMonHoc": 1,
  "idLop": 1,
  "idHocKy": 1,
  "idPhong": 2,
  "idBuoi": 1,
  "idHinhThuc": 1,
  "thuHoc": 3,
  "soTiet": 4,
  "heSo": 1.0,
  "nhomTh": "Nhóm 1",
  "tuanHoc": "1-15",
  "ghiChu": "Cập nhật ghi chú"
}
```

### 6. Xóa Lịch Giảng
**Quyền**: ADMIN, TRUONG_KHOA
```http
DELETE /api/schedules/{id}
Authorization: Bearer {token}
```

### 7. Lấy Lịch Giảng Cá Nhân
**Quyền**: GIANG_VIEN
```http
GET /api/schedules/personal?semesterId={semesterId}
Authorization: Bearer {token}
```

### 8. Kiểm Tra Phòng Học Khả Dụng
**Quyền**: ADMIN, TRUONG_KHOA
```http
GET /api/schedules/available-rooms?coSoId=1&loaiPhong=LT&thuHoc=2&buoiId=1&hocKyId=1
Authorization: Bearer {token}
```

## ⏰ Quản Lý Giờ Giảng

### 1. Lấy Giờ Giảng Theo Giảng Viên
```http
GET /api/teaching-hours/teacher/{teacherId}?semesterId={semesterId}
Authorization: Bearer {token}
```

### 2. Lấy Giờ Giảng Cá Nhân
**Quyền**: GIANG_VIEN
```http
GET /api/teaching-hours/personal?semesterId={semesterId}
Authorization: Bearer {token}
```

### 3. Lấy Giờ Giảng Theo Khoa
**Quyền**: ADMIN, TRUONG_KHOA
```http
GET /api/teaching-hours/department/{departmentId}?semesterId={semesterId}
Authorization: Bearer {token}
```

### 4. Tính Toán Giờ Giảng
**Quyền**: ADMIN, TRUONG_KHOA
```http
POST /api/teaching-hours/calculate?teacherId={teacherId}&semesterId={semesterId}
Authorization: Bearer {token}
```

### 5. Tính Toán Tất Cả Giờ Giảng
**Quyền**: ADMIN, TRUONG_KHOA
```http
POST /api/teaching-hours/calculate-all?semesterId={semesterId}
Authorization: Bearer {token}
```

## 📊 Xuất Báo Cáo

### 1. Xuất Lịch Giảng Cá Nhân
**Quyền**: GIANG_VIEN
```http
GET /api/schedules/export/personal?teacherId={teacherId}&semesterId={semesterId}
Authorization: Bearer {token}
```

### 2. Xuất Báo Cáo Giờ Giảng
**Quyền**: ADMIN, TRUONG_KHOA
```http
GET /api/teaching-hours/export?departmentId={departmentId}&semesterId={semesterId}
Authorization: Bearer {token}
```

## 🏥 Health Check

### Kiểm Tra Trạng Thái Hệ Thống
```http
GET /api/health
```

### Thông Tin Hệ Thống
```http
GET /api/health/info
```

## 📝 Cấu Trúc Response Chung

### Success Response
```json
{
  "success": true,
  "message": "Thành công",
  "data": { ... },
  "timestamp": "2024-01-01T10:00:00"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Lỗi xảy ra",
  "data": null,
  "timestamp": "2024-01-01T10:00:00"
}
```

### Paginated Response
```json
{
  "success": true,
  "message": "Thành công",
  "data": {
    "content": [...],
    "totalElements": 100,
    "totalPages": 10,
    "size": 10,
    "number": 0,
    "first": true,
    "last": false
  }
}
```

## 🔒 Phân Quyền

### ADMIN
- Toàn quyền truy cập tất cả chức năng
- Quản lý lịch giảng toàn trường
- Xem báo cáo tất cả khoa

### TRUONG_KHOA
- Quản lý lịch giảng trong khoa
- Xem báo cáo giờ giảng khoa
- Tính toán giờ giảng

### GIANG_VIEN
- Xem lịch giảng cá nhân
- Xem báo cáo giờ giảng cá nhân
- Xuất báo cáo cá nhân

## ⚠️ Lưu Ý Quan Trọng

1. **Token Expiration**: JWT token có thời hạn 24 giờ
2. **Rate Limiting**: Hệ thống có thể áp dụng giới hạn số request
3. **Data Validation**: Tất cả input đều được validate
4. **Error Handling**: Lỗi được trả về với mã HTTP và message rõ ràng
5. **Timezone**: Hệ thống sử dụng UTC timezone

## 🚀 Bắt Đầu Nhanh

1. **Khởi động ứng dụng**:
   ```bash
   mvn spring-boot:run
   ```

2. **Truy cập Swagger UI**:
   ```
   http://localhost:8080/api/swagger-ui.html
   ```

3. **Đăng nhập với tài khoản admin**:
   ```json
   {
     "maCanBo": "admin",
     "matKhau": "123456"
   }
   ```

4. **Copy token và sử dụng trong Swagger**:
   - Click "Authorize" button
   - Nhập: `Bearer {your-token}`

5. **Test các API**:
   - Bắt đầu với `/health` để kiểm tra hệ thống
   - Thử `/auth/me` để xem thông tin user
   - Tạo lịch giảng mới với `/schedules`
