package com.university.schedulemanagement.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Nhom Entity - Nhóm môn học
 */
@Entity
@Table(name = "NHOM")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class Nhom extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_NHOM")
    private Long idNhom;

    @Column(name = "ID_MON_HOC", nullable = false)
    private Long idMonHoc;

    @Column(name = "ID_CAN_BO", nullable = false)
    private Long idCanBo;

    @Column(name = "TEN_NHOM", nullable = false, length = 250)
    private String tenNhom;

    @Column(name = "HOC_LAI")
    private Boolean hocLai = false;

    @Column(name = "DUYET_DIEM")
    private Boolean duyetDiem = false;

    @Column(name = "ID_CAN_BO_GD")
    private Long idCanBoGd;

    @Column(name = "CONG_BO_DIEM")
    private Boolean congBoDiem = false;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_MON_HOC", insertable = false, updatable = false)
    private MonHoc monHoc;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_CAN_BO", insertable = false, updatable = false)
    private CanBo canBo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_CAN_BO_GD", insertable = false, updatable = false)
    private CanBo canBoGiamDoc;

    // Helper methods
    public String getNhomInfo() {
        return String.format("%s - %s (%s)",
                tenNhom,
                monHoc != null ? monHoc.getTenMonHoc() : "",
                canBo != null ? canBo.getTen() : ""
        );
    }

    public String getTrangThaiDiem() {
        if (congBoDiem) {
            return "Đã công bố";
        } else if (duyetDiem) {
            return "Đã duyệt";
        } else {
            return "Chưa duyệt";
        }
    }
}
